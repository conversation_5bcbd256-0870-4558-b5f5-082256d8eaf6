#!/usr/bin/env python3
"""
OWL检测API测试脚本
"""

import os
import sys
import json
import time
import requests
import argparse
from typing import Dict, Any, List


class OWLDetectionAPITester:
    """OWL检测API测试类"""
    
    def __init__(self, base_url: str):
        """
        初始化测试器
        
        Args:
            base_url: API服务基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 30
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        print("测试健康检查接口...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 健康检查通过: {data.get('message', '')}")
                return True
            else:
                print(f"✗ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 健康检查异常: {e}")
            return False
    
    def test_service_info(self) -> bool:
        """测试服务信息接口"""
        print("测试服务信息接口...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/info")
            if response.status_code == 200:
                data = response.json()
                service_info = data.get('data', {})
                print(f"✓ 服务信息获取成功:")
                print(f"  - 服务名称: {service_info.get('service_name', 'N/A')}")
                print(f"  - 文本模型: {service_info.get('text_model', 'N/A')}")
                print(f"  - 视觉模型: {service_info.get('visual_model', 'N/A')}")
                print(f"  - 默认阈值: {service_info.get('default_threshold', 'N/A')}")
                return True
            else:
                print(f"✗ 服务信息获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 服务信息获取异常: {e}")
            return False
    
    def test_detection(self, image_path: str, query_texts: List[str], 
                      threshold: float = None, nms_threshold: float = None) -> bool:
        """
        测试检测接口
        
        Args:
            image_path: 测试图片路径
            query_texts: 查询文本列表
            threshold: 检测阈值
            nms_threshold: NMS阈值
        """
        print(f"测试检测接口...")
        print(f"  - 图片: {image_path}")
        print(f"  - 查询文本: {query_texts}")
        print(f"  - 阈值: {threshold}")
        print(f"  - NMS阈值: {nms_threshold}")
        
        try:
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                print(f"✗ 图片文件不存在: {image_path}")
                return False
            
            # 准备请求数据
            files = {'image': open(image_path, 'rb')}
            data = {'query_texts': json.dumps(query_texts)}
            
            if threshold is not None:
                data['threshold'] = str(threshold)
            if nms_threshold is not None:
                data['nms_threshold'] = str(nms_threshold)
            
            # 发送请求
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/v1/detect",
                files=files,
                data=data
            )
            request_time = time.time() - start_time
            
            files['image'].close()
            
            if response.status_code == 200:
                result = response.json()
                detections = result.get('data', {}).get('detections', [])
                total_count = result.get('data', {}).get('total_count', 0)
                processing_time = result.get('data', {}).get('processing_time', 0)
                
                print(f"✓ 检测成功:")
                print(f"  - 检测到目标数量: {total_count}")
                print(f"  - 处理时间: {processing_time:.3f}s")
                print(f"  - 请求时间: {request_time:.3f}s")
                
                # 显示检测结果
                for i, detection in enumerate(detections):
                    bbox = detection.get('bbox', [])
                    score = detection.get('score', 0)
                    query_text = detection.get('query_text', '')
                    print(f"  - 目标{i+1}: {query_text}, 分数: {score:.3f}, 位置: {bbox}")
                
                return True
            else:
                print(f"✗ 检测失败: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"  错误信息: {error_data.get('message', 'Unknown error')}")
                except:
                    print(f"  响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 检测异常: {e}")
            return False
    
    def test_invalid_requests(self) -> bool:
        """测试无效请求"""
        print("测试无效请求处理...")
        
        tests = [
            {
                "name": "缺少图片文件",
                "data": {"query_texts": json.dumps(["person"])},
                "files": {},
                "expected_code": 400
            },
            {
                "name": "空查询文本",
                "data": {"query_texts": json.dumps([])},
                "files": {"image": ("test.jpg", b"fake_image_data", "image/jpeg")},
                "expected_code": 400
            },
            {
                "name": "无效阈值",
                "data": {
                    "query_texts": json.dumps(["person"]),
                    "threshold": "invalid"
                },
                "files": {"image": ("test.jpg", b"fake_image_data", "image/jpeg")},
                "expected_code": 400
            }
        ]
        
        all_passed = True
        for test in tests:
            try:
                response = self.session.post(
                    f"{self.base_url}/api/v1/detect",
                    files=test["files"],
                    data=test["data"]
                )
                
                if response.status_code == test["expected_code"]:
                    print(f"✓ {test['name']}: 正确返回 HTTP {response.status_code}")
                else:
                    print(f"✗ {test['name']}: 期望 HTTP {test['expected_code']}, 实际 HTTP {response.status_code}")
                    all_passed = False
                    
            except Exception as e:
                print(f"✗ {test['name']}: 异常 {e}")
                all_passed = False
        
        return all_passed
    
    def run_all_tests(self, image_path: str = None, query_texts: List[str] = None) -> bool:
        """运行所有测试"""
        print("=" * 60)
        print("OWL检测API测试")
        print("=" * 60)
        print(f"测试目标: {self.base_url}")
        print()
        
        all_passed = True
        
        # 基础接口测试
        all_passed &= self.test_health_check()
        print()
        
        all_passed &= self.test_service_info()
        print()
        
        # 检测功能测试
        if image_path and query_texts:
            all_passed &= self.test_detection(image_path, query_texts)
            print()
        
        # 错误处理测试
        all_passed &= self.test_invalid_requests()
        print()
        
        # 测试结果
        print("=" * 60)
        if all_passed:
            print("✓ 所有测试通过")
        else:
            print("✗ 部分测试失败")
        print("=" * 60)
        
        return all_passed


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OWL检测API测试脚本")
    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8082",
        help="API服务URL"
    )
    parser.add_argument(
        "--image",
        type=str,
        help="测试图片路径"
    )
    parser.add_argument(
        "--query-texts",
        type=str,
        nargs="+",
        default=["person", "car", "cat"],
        help="查询文本列表"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        help="检测阈值"
    )
    parser.add_argument(
        "--nms-threshold",
        type=float,
        help="NMS阈值"
    )
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = OWLDetectionAPITester(args.url)
    
    # 运行测试
    success = tester.run_all_tests(args.image, args.query_texts)
    
    # 如果提供了图片，进行详细检测测试
    if args.image:
        print("\n详细检测测试:")
        tester.test_detection(
            args.image, 
            args.query_texts, 
            args.threshold, 
            args.nms_threshold
        )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
