package com.bohua.scheduler.service;

import com.bohua.scheduler.dto.ScheduleRequest;
import com.bohua.scheduler.dto.ScheduleResult;
import com.bohua.scheduler.dto.ServiceInfo;
import com.bohua.scheduler.dto.TaskInfo;
import com.bohua.scheduler.exception.NoAvailableServiceException;
import com.bohua.scheduler.exception.NotFoundException;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.ScheduleStrategy;
import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.ScheduleMode;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.model.enums.TaskStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import com.bohua.scheduler.repository.ScheduleStrategyRepository;
import com.bohua.scheduler.repository.TaskAllocationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.ArrayList;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.stream.Collectors;

/**
 * 调度服务
 */
@Service
@Slf4j
public class SchedulerService {
    
    @Autowired
    private InferenceServiceRepository serviceRepository;
    
    @Autowired
    private TaskAllocationRepository allocationRepository;
    
    @Autowired
    private ScheduleStrategyRepository strategyRepository;
    
    @Autowired
    private LockService lockService;

    @Autowired
    private RestTemplate restTemplate;

    private static final String QUOTA_LOCK_PREFIX = "quota_lock:";
    private static final long LOCK_TIMEOUT = 10; // 10秒
    
    /**
     * 调度任务到推理服务
     */
    @Transactional
    public ScheduleResult scheduleTask(ScheduleRequest request) {
        log.info("开始调度任务: taskId={}, deviceId={}",
                request.getTaskRequest().getTaskId(),
                request.getTaskRequest().getDevice().getDeviceId());
        
        try {
            // 1. 获取调度策略
            ScheduleStrategy strategy = getActiveStrategy();
            
            // 2. 获取可用服务列表
            List<InferenceService> availableServices = getAvailableServices(request.getRegion());
            
            if (availableServices.isEmpty()) {
                throw new NoAvailableServiceException("没有可用的推理服务");
            }
            
            // 3. 根据策略选择服务
            InferenceService selectedService = selectService(availableServices, request, strategy);
            
            // 4. 使用锁分配任务
            String lockKey = QUOTA_LOCK_PREFIX + selectedService.getServiceId();

            return lockService.executeWithLock(lockKey, LOCK_TIMEOUT, () -> {
                // 5. 再次检查服务可用性
                InferenceService service = serviceRepository.findById(selectedService.getServiceId())
                    .orElseThrow(() -> new NotFoundException("服务不存在"));

                if (!service.isAvailable()) {
                    throw new NoAvailableServiceException("服务已不可用");
                }

                // 6. 分配任务（幂等操作）
                TaskAllocationResult allocationResult = allocateTask(request, service);
                TaskAllocation allocation = allocationResult.getAllocation();

                // 7. 只有新创建的任务才需要更新quota和发送任务
                if (allocationResult.isNewlyCreated()) {
                    // 更新服务quota
                    updateServiceQuota(service.getServiceId(), 1);

                    // 向推理服务发送任务
                    boolean taskSent = sendTaskToInferenceService(request, service);
                    if (!taskSent) {
                        // 任务发送失败，回滚操作
                        log.error("向推理服务发送任务失败，开始回滚: taskId={}, serviceId={}",
                                request.getTaskRequest().getTaskId(), service.getServiceId());

                        // 回滚quota
                        updateServiceQuota(service.getServiceId(), -1);

                        // 更新任务状态为失败
                        allocation.setTaskStatus(TaskStatus.ERROR);
                        allocation.setErrorMessage("向推理服务发送任务失败");
                        allocation.setEndTime(LocalDateTime.now());
                        allocationRepository.save(allocation);

                        throw new RuntimeException("向推理服务发送任务失败");
                    }

                    // 任务发送成功，更新状态为运行中
                    allocation.setTaskStatus(TaskStatus.RUNNING);
                    allocation.setStartTime(LocalDateTime.now());
                    allocationRepository.save(allocation);

                } else {
                    log.info("任务已存在，跳过quota更新和任务发送: taskId={}",
                        request.getTaskRequest().getTaskId());
                }

                log.info("任务调度成功: taskId={}, serviceId={}, allocationId={}",
                        request.getTaskRequest().getTaskId(), service.getServiceId(), allocation.getAllocationId());

                return ScheduleResult.builder()
                    .success(true)
                    .taskId(request.getTaskRequest().getTaskId())
                    .serviceId(service.getServiceId())
                    .serviceUrl(service.getBaseUrl())
                    .build();
            });
            
        } catch (Exception e) {
            log.error("任务调度失败: taskId={}, error={}", request.getTaskRequest().getTaskId(), e.getMessage(), e);
            return ScheduleResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .errorCode(e instanceof NoAvailableServiceException ? "NO_AVAILABLE_SERVICE" : "SCHEDULE_ERROR")
                .build();
        }
    }
    
    /**
     * 释放任务资源（通过allocationId）
     */
    @Transactional
    public void releaseTask(String allocationId) {
        log.info("释放任务资源: allocationId={}", allocationId);

        TaskAllocation allocation = allocationRepository.findById(allocationId)
            .orElseThrow(() -> new NotFoundException("任务分配记录不存在"));

        // 更新任务状态
        allocation.setTaskStatus(TaskStatus.STOPPED);
        allocation.setEndTime(LocalDateTime.now());
        allocationRepository.save(allocation);

        // 释放服务quota
        updateServiceQuota(allocation.getServiceId(), -1);

        log.info("任务资源释放成功: allocationId={}, serviceId={}", allocationId, allocation.getServiceId());
    }

    /**
     * 释放任务资源（通过taskId）
     */
    @Transactional
    public void releaseTaskByTaskId(String taskId) {
        log.info("释放任务资源: taskId={}", taskId);

        // 查找活跃的任务分配
        List<TaskAllocation> activeAllocations = allocationRepository.findActiveTasksByTaskId(taskId);

        if (activeAllocations.isEmpty()) {
            throw new NotFoundException("任务不存在或已释放");
        }

        // 释放所有活跃的分配（通常只有一个）
        for (TaskAllocation allocation : activeAllocations) {
            String serviceId = allocation.getServiceId();
            String allocationId = allocation.getAllocationId();

            log.info("开始释放任务分配: taskId={}, allocationId={}, serviceId={}, currentStatus={}",
                    taskId, allocationId, serviceId, allocation.getTaskStatus());

            // 1. 先尝试从推理服务删除任务
            boolean inferenceDeleted = false;
            String errorMessage = null;

            try {
                Optional<InferenceService> serviceOpt = serviceRepository.findById(serviceId);
                if (serviceOpt.isPresent()) {
                    InferenceService service = serviceOpt.get();

                    // 检查服务状态
                    if (!service.isAvailable()) {
                        log.warn("推理服务不可用，跳过任务删除: taskId={}, serviceId={}, serviceStatus={}",
                                taskId, serviceId, service.getStatus());
                        errorMessage = "推理服务不可用，无法删除任务";
                    } else {
                        log.info("开始从推理服务删除任务: taskId={}, serviceId={}, serviceUrl={}",
                                taskId, serviceId, service.getBaseUrl());

                        inferenceDeleted = deleteTaskFromInferenceService(taskId, service);

                        if (!inferenceDeleted) {
                            errorMessage = "推理服务任务删除失败";
                            log.warn("推理服务任务删除失败，但继续释放Scheduler资源: taskId={}, serviceId={}",
                                    taskId, serviceId);
                        } else {
                            log.info("推理服务任务删除成功: taskId={}, serviceId={}", taskId, serviceId);
                        }
                    }
                } else {
                    errorMessage = "推理服务记录不存在";
                    log.warn("推理服务记录不存在，跳过推理服务任务删除: taskId={}, serviceId={}",
                            taskId, serviceId);
                }
            } catch (Exception e) {
                errorMessage = "调用推理服务删除任务时发生异常: " + e.getMessage();
                log.error("调用推理服务删除任务时发生异常，但继续释放Scheduler资源: taskId={}, serviceId={}, error={}",
                        taskId, serviceId, e.getMessage(), e);
            }

            // 2. 更新Scheduler中的任务状态（无论推理服务删除是否成功）
            try {
                allocation.setTaskStatus(TaskStatus.STOPPED);
                allocation.setEndTime(LocalDateTime.now());

                // 记录错误信息（如果有）
                if (errorMessage != null) {
                    String fullErrorMessage = errorMessage + "，但Scheduler资源已释放";
                    allocation.setErrorMessage(fullErrorMessage);
                }

                allocationRepository.save(allocation);
                log.info("任务分配状态更新成功: taskId={}, allocationId={}, newStatus={}",
                        taskId, allocationId, TaskStatus.STOPPED);

            } catch (Exception e) {
                log.error("更新任务分配状态失败: taskId={}, allocationId={}, error={}",
                        taskId, allocationId, e.getMessage(), e);
                throw new RuntimeException("更新任务状态失败", e);
            }

            // 3. 释放服务quota
            try {
                updateServiceQuota(serviceId, -1);
                log.info("服务配额释放成功: taskId={}, serviceId={}", taskId, serviceId);
            } catch (Exception e) {
                log.error("释放服务配额失败: taskId={}, serviceId={}, error={}",
                        taskId, serviceId, e.getMessage(), e);
                throw new RuntimeException("释放服务配额失败", e);
            }

            log.info("任务资源释放完成: taskId={}, allocationId={}, serviceId={}, inferenceDeleted={}, hasError={}",
                    taskId, allocationId, serviceId, inferenceDeleted, errorMessage != null);
        }
    }
    
    /**
     * 获取所有服务信息
     */
    public List<ServiceInfo> getAllServices() {
        return serviceRepository.findAll().stream()
            .map(this::convertToServiceInfo)
            .collect(Collectors.toList());
    }

    /**
     * 批量查询任务信息
     */
    public List<TaskInfo> queryTasks(List<String> taskIds) {
        List<TaskAllocation> allocations;

        if (taskIds == null || taskIds.isEmpty()) {
            // 查询所有活跃任务
            allocations = allocationRepository.findByTaskStatusIn(
                Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));
        } else {
            // 查询指定任务ID的活跃任务
            allocations = new ArrayList<>();
            for (String taskId : taskIds) {
                allocations.addAll(allocationRepository.findActiveTasksByTaskId(taskId));
            }
        }

        return allocations.stream()
            .map(this::convertToTaskInfo)
            .collect(Collectors.toList());
    }

    /**
     * 查询特定任务信息
     */
    public TaskInfo getTaskInfo(String taskId) {
        List<TaskAllocation> activeAllocations = allocationRepository.findActiveTasksByTaskId(taskId);

        if (activeAllocations.isEmpty()) {
            throw new NotFoundException("任务不存在");
        }

        // 通常一个taskId只有一个活跃的分配
        TaskAllocation allocation = activeAllocations.get(0);
        return convertToTaskInfo(allocation);
    }
    
    /**
     * 获取活跃调度策略
     */
    private ScheduleStrategy getActiveStrategy() {
        return strategyRepository.findDefaultStrategy()
            .orElse(ScheduleStrategy.builder()
                .scheduleMode(ScheduleMode.FILL_FIRST)
                .enableDynamicSchedule(true)
                .build());
    }
    
    /**
     * 获取可用服务列表
     */
    private List<InferenceService> getAvailableServices(String region) {
        List<InferenceService> services;
        if (region != null && !region.isEmpty()) {
            services = serviceRepository.findByRegionAndStatus(region, ServiceStatus.ACTIVE);
        } else {
            services = serviceRepository.findByStatus(ServiceStatus.ACTIVE);
        }

        // 在应用层过滤：只返回有剩余quota的服务
        return services.stream()
            .filter(service -> service.getCurrentQuota() < service.getMaxQuota())
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 根据策略选择服务
     */
    private InferenceService selectService(List<InferenceService> services,
                                         ScheduleRequest request,
                                         ScheduleStrategy strategy) {
        switch (strategy.getScheduleMode()) {
            case FILL_FIRST:
                return selectByFillFirst(services);
            case SPREAD_FIRST:
                return selectBySpreadFirst(services);
            default:
                return selectByFillFirst(services);
        }
    }
    
    /**
     * 优先沾满策略：优先选择已有任务较多的服务
     */
    private InferenceService selectByFillFirst(List<InferenceService> services) {
        return services.stream()
            .filter(InferenceService::isAvailable)
            .max(Comparator.comparing(InferenceService::getCurrentQuota))
            .orElseThrow(() -> new NoAvailableServiceException("没有可用的推理服务"));
    }
    
    /**
     * 优先平铺策略：优先选择负载最轻的服务
     */
    private InferenceService selectBySpreadFirst(List<InferenceService> services) {
        return services.stream()
            .filter(InferenceService::isAvailable)
            .min(Comparator.comparing(InferenceService::getCurrentQuota))
            .orElseThrow(() -> new NoAvailableServiceException("没有可用的推理服务"));
    }
    
    /**
     * 分配任务（幂等操作）
     */
    private TaskAllocationResult allocateTask(ScheduleRequest request, InferenceService service) {
        String taskId = request.getTaskRequest().getTaskId();

        // 检查任务是否已经存在（幂等性检查）
        List<TaskAllocation> existingTasks = allocationRepository.findActiveTasksByTaskId(taskId);

        if (!existingTasks.isEmpty()) {
            // 任务已存在，返回现有的任务分配
            TaskAllocation existingTask = existingTasks.get(0);
            log.info("任务已存在，返回现有分配: taskId={}, allocationId={}, serviceId={}",
                taskId, existingTask.getAllocationId(), existingTask.getServiceId());

            // 检查现有任务是否分配给了同一个服务
            if (!existingTask.getServiceId().equals(service.getServiceId())) {
                log.warn("任务已分配给其他服务: taskId={}, currentService={}, requestedService={}",
                    taskId, existingTask.getServiceId(), service.getServiceId());
            }

            return new TaskAllocationResult(existingTask, false);
        }

        // 任务不存在，创建新的任务分配
        TaskAllocation allocation = TaskAllocation.builder()
            .allocationId(UUID.randomUUID().toString())
            .taskId(taskId)
            .deviceId(request.getTaskRequest().getDevice().getDeviceId())
            .serviceId(service.getServiceId())
            .taskStatus(TaskStatus.ALLOCATED)
            .taskRequest(request.getTaskRequest())
            .build();

        TaskAllocation savedAllocation = allocationRepository.save(allocation);
        log.info("创建新任务分配: taskId={}, allocationId={}, serviceId={}",
            taskId, savedAllocation.getAllocationId(), savedAllocation.getServiceId());

        return new TaskAllocationResult(savedAllocation, true);
    }
    
    /**
     * 更新服务quota
     */
    private void updateServiceQuota(String serviceId, int delta) {
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new NotFoundException("服务不存在"));
        
        int newQuota = service.getCurrentQuota() + delta;
        if (newQuota < 0) {
            newQuota = 0;
        }
        if (newQuota > service.getMaxQuota()) {
            newQuota = service.getMaxQuota();
        }
        
        service.setCurrentQuota(newQuota);
        serviceRepository.save(service);
    }
    
    /**
     * 转换为ServiceInfo
     */
    private ServiceInfo convertToServiceInfo(InferenceService service) {
        return ServiceInfo.builder()
            .serviceId(service.getServiceId())
            .serviceName(service.getServiceName())
            .baseUrl(service.getBaseUrl())
            .maxQuota(service.getMaxQuota())
            .currentQuota(service.getCurrentQuota())
            .status(service.getStatus())
            .gpuType(service.getGpuType())
            .region(service.getRegion())
            .createTime(service.getCreateTime())
            .updateTime(service.getUpdateTime())
            .tags(service.getTags())
            .loadRate(service.getLoadRate())
            .availableQuota(service.getAvailableQuota())
            .build();
    }

    /**
     * 转换为TaskInfo
     */
    private TaskInfo convertToTaskInfo(TaskAllocation allocation) {
        // 获取服务信息
        InferenceService service = serviceRepository.findById(allocation.getServiceId()).orElse(null);
        String serviceUrl = service != null ? service.getBaseUrl() : null;
        String region = service != null ? service.getRegion() : "default";

        return TaskInfo.builder()
            .taskId(allocation.getTaskId())
            .taskName(allocation.getTaskRequest() != null ? allocation.getTaskRequest().getTaskName() : null)
            .taskDescription(allocation.getTaskRequest() != null ? allocation.getTaskRequest().getTaskDescription() : null)
            .status(allocation.getTaskStatus())
            .serviceId(allocation.getServiceId())
            .serviceUrl(serviceUrl)
            .region(region)
            .createTime(allocation.getAllocateTime())
            .updateTime(allocation.getStartTime() != null ? allocation.getStartTime() : allocation.getAllocateTime())
            .deviceId(allocation.getDeviceId())
            .taskMeta(allocation.getTaskRequest() != null ? allocation.getTaskRequest().getTaskMeta() : null)
            .algorithmOrchestration(allocation.getTaskRequest() != null ? allocation.getTaskRequest().getAlgorithmOrchestration() : null)
            .device(allocation.getTaskRequest() != null ? allocation.getTaskRequest().getDevice() : null)
            .build();
    }

    /**
     * 向推理服务发送任务
     */
    private boolean sendTaskToInferenceService(ScheduleRequest request, InferenceService service) {
        try {
            String taskUrl = service.getBaseUrl() + "/api/v1/tasks";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求体 - 发送完整的ScheduleRequest而不是只发送TaskRequest
            HttpEntity<Object> requestEntity = new HttpEntity<>(request, headers);

            log.info("向推理服务发送任务: url={}, taskId={}", taskUrl, request.getTaskRequest().getTaskId());

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                taskUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("任务发送成功: taskId={}, serviceId={}, response={}",
                        request.getTaskRequest().getTaskId(), service.getServiceId(), response.getBody());
                return true;
            } else {
                log.error("任务发送失败: taskId={}, serviceId={}, status={}, response={}",
                        request.getTaskRequest().getTaskId(), service.getServiceId(),
                        response.getStatusCode(), response.getBody());
                return false;
            }

        } catch (Exception e) {
            log.error("向推理服务发送任务异常: taskId={}, serviceId={}, error={}",
                    request.getTaskRequest().getTaskId(), service.getServiceId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从推理服务删除任务（带重试机制）
     */
    private boolean deleteTaskFromInferenceService(String taskId, InferenceService service) {
        final int maxRetries = 3;
        final long retryDelayMs = 1000; // 1秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                String deleteUrl = service.getBaseUrl() + "/api/v1/tasks/" + taskId;

                log.info("向推理服务发送删除任务请求 (尝试 {}/{}): url={}, taskId={}, serviceId={}",
                        attempt, maxRetries, deleteUrl, taskId, service.getServiceId());

                // 发送DELETE请求
                ResponseEntity<String> response = restTemplate.exchange(
                    deleteUrl,
                    HttpMethod.DELETE,
                    null,
                    String.class
                );

                if (response.getStatusCode().is2xxSuccessful()) {
                    log.info("推理服务任务删除成功: taskId={}, serviceId={}, attempt={}, response={}",
                            taskId, service.getServiceId(), attempt, response.getBody());
                    return true;
                } else if (response.getStatusCode().value() == 404) {
                    // 任务在推理服务中不存在，视为删除成功
                    log.warn("推理服务中任务不存在，视为删除成功: taskId={}, serviceId={}, attempt={}",
                            taskId, service.getServiceId(), attempt);
                    return true;
                } else {
                    log.error("推理服务任务删除失败 (尝试 {}/{}): taskId={}, serviceId={}, status={}, response={}",
                            attempt, maxRetries, taskId, service.getServiceId(),
                            response.getStatusCode(), response.getBody());

                    // 如果不是最后一次尝试，等待后重试
                    if (attempt < maxRetries) {
                        Thread.sleep(retryDelayMs);
                    }
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("删除任务请求被中断: taskId={}, serviceId={}, attempt={}",
                        taskId, service.getServiceId(), attempt);
                return false;
            } catch (Exception e) {
                log.error("向推理服务发送删除任务请求异常 (尝试 {}/{}): taskId={}, serviceId={}, error={}",
                        attempt, maxRetries, taskId, service.getServiceId(), e.getMessage(), e);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试等待被中断: taskId={}, serviceId={}", taskId, service.getServiceId());
                        return false;
                    }
                }
            }
        }

        log.error("推理服务任务删除最终失败，已尝试 {} 次: taskId={}, serviceId={}",
                maxRetries, taskId, service.getServiceId());
        return false;
    }

    /**
     * 重试失败的任务
     */
    @Transactional
    public void retryFailedTasks() {
        log.info("开始重试失败的任务");

        // 查询所有失败的任务
        List<TaskAllocation> failedTasks = allocationRepository.findByTaskStatus(TaskStatus.ERROR);

        for (TaskAllocation failedTask : failedTasks) {
            try {
                // 检查服务是否可用
                Optional<InferenceService> serviceOpt = serviceRepository.findById(failedTask.getServiceId());
                if (!serviceOpt.isPresent() || !serviceOpt.get().isAvailable()) {
                    log.warn("服务不可用，跳过重试: taskId={}, serviceId={}",
                            failedTask.getTaskId(), failedTask.getServiceId());
                    continue;
                }

                InferenceService service = serviceOpt.get();

                // 重新构造请求
                ScheduleRequest retryRequest = ScheduleRequest.builder()
                    .taskRequest(failedTask.getTaskRequest())
                    .region("default")
                    .priority(1)
                    .build();

                // 尝试重新发送任务
                boolean taskSent = sendTaskToInferenceService(retryRequest, service);
                if (taskSent) {
                    // 重试成功，更新状态
                    failedTask.setTaskStatus(TaskStatus.RUNNING);
                    failedTask.setStartTime(LocalDateTime.now());
                    failedTask.setErrorMessage(null);
                    allocationRepository.save(failedTask);

                    // 重新分配quota
                    updateServiceQuota(service.getServiceId(), 1);

                    log.info("任务重试成功: taskId={}, serviceId={}",
                            failedTask.getTaskId(), failedTask.getServiceId());
                } else {
                    log.warn("任务重试失败: taskId={}, serviceId={}",
                            failedTask.getTaskId(), failedTask.getServiceId());
                }

            } catch (Exception e) {
                log.error("重试任务异常: taskId={}, error={}", failedTask.getTaskId(), e.getMessage(), e);
            }
        }

        log.info("失败任务重试完成，处理了{}个任务", failedTasks.size());
    }

    /**
     * 清理孤儿任务（双向检查：Scheduler和推理服务的任务状态一致性）
     */
    @Transactional
    public void cleanupOrphanTasks() {
        log.info("开始清理孤儿任务");

        // 1. 清理Scheduler有记录但推理服务没有的任务
        cleanupSchedulerOrphanTasks();

        // 2. 清理推理服务有任务但Scheduler没有记录的任务
        cleanupInferenceOrphanTasks();

        log.info("孤儿任务清理完成");
    }

    /**
     * 清理Scheduler有记录但推理服务没有的任务
     */
    private void cleanupSchedulerOrphanTasks() {
        log.debug("开始清理Scheduler孤儿任务");

        // 查询所有活跃任务
        List<TaskAllocation> activeTasks = allocationRepository.findByTaskStatusIn(
            Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));

        for (TaskAllocation task : activeTasks) {
            try {
                // 检查推理服务中是否存在该任务
                Optional<InferenceService> serviceOpt = serviceRepository.findById(task.getServiceId());
                if (!serviceOpt.isPresent()) {
                    // 服务不存在，标记任务为失败
                    task.setTaskStatus(TaskStatus.ERROR);
                    task.setErrorMessage("推理服务不存在");
                    task.setEndTime(LocalDateTime.now());
                    allocationRepository.save(task);
                    continue;
                }

                InferenceService service = serviceOpt.get();

                // 调用推理服务检查任务是否存在
                String checkUrl = service.getBaseUrl() + "/api/v1/tasks/" + task.getTaskId();
                try {
                    ResponseEntity<String> response = restTemplate.getForEntity(checkUrl, String.class);
                    if (!response.getStatusCode().is2xxSuccessful()) {
                        // 任务不存在，重新分发孤儿任务
                        handleOrphanTask(task, service, "推理服务中任务不存在（孤儿任务）");
                    }
                } catch (Exception e) {
                    // 检查是否是404错误（任务不存在）
                    if (e.getMessage() != null && e.getMessage().contains("404")) {
                        // 404错误表示任务不存在，重新分发孤儿任务
                        handleOrphanTask(task, service, "推理服务中任务不存在（404错误）");
                    } else {
                        log.debug("检查任务存在性失败，可能是推理服务不可达: taskId={}, error={}",
                                task.getTaskId(), e.getMessage());
                    }
                }

            } catch (Exception e) {
                log.error("清理Scheduler孤儿任务异常: taskId={}, error={}", task.getTaskId(), e.getMessage(), e);
            }
        }

        log.debug("Scheduler孤儿任务清理完成");
    }

    /**
     * 清理推理服务有任务但Scheduler没有记录的任务
     */
    private void cleanupInferenceOrphanTasks() {
        log.debug("开始清理推理服务孤儿任务");

        // 获取所有活跃的推理服务
        List<InferenceService> activeServices = serviceRepository.findByStatus(ServiceStatus.ACTIVE);

        for (InferenceService service : activeServices) {
            try {
                // 获取推理服务中的所有任务
                String tasksUrl = service.getBaseUrl() + "/api/v1/tasks";
                ResponseEntity<String> response = restTemplate.getForEntity(tasksUrl, String.class);

                if (!response.getStatusCode().is2xxSuccessful()) {
                    log.debug("无法获取推理服务任务列表: serviceId={}, status={}",
                            service.getServiceId(), response.getStatusCode());
                    continue;
                }

                // 解析推理服务返回的任务列表
                List<String> inferenceTaskIds = parseTaskIdsFromResponse(response.getBody());

                // 获取Scheduler中该服务的所有活跃任务
                List<TaskAllocation> schedulerTasks = allocationRepository.findByServiceIdAndTaskStatusIn(
                    service.getServiceId(), Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));

                Set<String> schedulerTaskIds = schedulerTasks.stream()
                    .map(TaskAllocation::getTaskId)
                    .collect(Collectors.toSet());

                // 找出推理服务中存在但Scheduler中没有记录的任务
                List<String> orphanTaskIds = inferenceTaskIds.stream()
                    .filter(taskId -> !schedulerTaskIds.contains(taskId))
                    .collect(Collectors.toList());

                // 删除推理服务中的孤儿任务
                for (String orphanTaskId : orphanTaskIds) {
                    deleteTaskFromInferenceService(service, orphanTaskId);
                }

                if (!orphanTaskIds.isEmpty()) {
                    log.info("清理推理服务孤儿任务: serviceId={}, 删除任务数={}, taskIds={}",
                            service.getServiceId(), orphanTaskIds.size(), orphanTaskIds);
                }

            } catch (Exception e) {
                log.error("清理推理服务孤儿任务异常: serviceId={}, error={}",
                        service.getServiceId(), e.getMessage(), e);
            }
        }

        log.debug("推理服务孤儿任务清理完成");
    }

    /**
     * 解析推理服务返回的任务列表，提取任务ID
     */
    private List<String> parseTaskIdsFromResponse(String responseBody) {
        List<String> taskIds = new ArrayList<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(responseBody);

            // 假设返回格式是 {"tasks": [{"task_id": "xxx", ...}, ...]}
            if (rootNode.has("tasks") && rootNode.get("tasks").isArray()) {
                for (JsonNode taskNode : rootNode.get("tasks")) {
                    if (taskNode.has("task_id")) {
                        taskIds.add(taskNode.get("task_id").asText());
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析推理服务任务列表失败: {}", e.getMessage(), e);
        }
        return taskIds;
    }

    /**
     * 从推理服务中删除指定任务
     */
    private void deleteTaskFromInferenceService(InferenceService service, String taskId) {
        try {
            String deleteUrl = service.getBaseUrl() + "/api/v1/tasks/" + taskId;
            ResponseEntity<String> response = restTemplate.exchange(
                deleteUrl, HttpMethod.DELETE, null, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("成功删除推理服务孤儿任务: serviceId={}, taskId={}",
                        service.getServiceId(), taskId);

                // 更新服务的当前配额
                service.setCurrentQuota(service.getCurrentQuota() - 1);
                serviceRepository.save(service);

            } else {
                log.warn("删除推理服务孤儿任务失败: serviceId={}, taskId={}, status={}",
                        service.getServiceId(), taskId, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("删除推理服务孤儿任务异常: serviceId={}, taskId={}, error={}",
                    service.getServiceId(), taskId, e.getMessage(), e);
        }
    }

    /**
     * 处理孤儿任务 - 重新分发任务
     */
    private void handleOrphanTask(TaskAllocation orphanTask, InferenceService originalService, String reason) {
        log.warn("发现孤儿任务，开始重新分发: taskId={}, serviceId={}, reason={}",
                orphanTask.getTaskId(), orphanTask.getServiceId(), reason);

        try {
            // 1. 回滚原服务的quota
            updateServiceQuota(originalService.getServiceId(), -1);

            // 2. 重新构造调度请求
            ScheduleRequest retryRequest = ScheduleRequest.builder()
                .taskRequest(orphanTask.getTaskRequest())
                .region("default")  // 使用默认region
                .priority(1)        // 使用默认priority
                .build();

            // 3. 删除原有的任务分配记录
            allocationRepository.delete(orphanTask);
            log.info("已删除孤儿任务的原分配记录: taskId={}, allocationId={}",
                    orphanTask.getTaskId(), orphanTask.getAllocationId());

            // 4. 重新调度任务（这会创建新的分配记录）
            ScheduleResult newSchedule = scheduleTask(retryRequest);

            if (newSchedule.isSuccess()) {
                log.info("孤儿任务重新分发成功: taskId={}, newServiceId={}",
                        orphanTask.getTaskId(), newSchedule.getServiceId());
            } else {
                log.error("孤儿任务重新分发失败: taskId={}, error={}",
                        orphanTask.getTaskId(), newSchedule.getErrorMessage());

                // 重新分发失败，创建失败记录
                TaskAllocation failedTask = TaskAllocation.builder()
                    .taskId(orphanTask.getTaskId())
                    .taskRequest(orphanTask.getTaskRequest())
                    .serviceId(originalService.getServiceId())
                    .taskStatus(TaskStatus.ERROR)
                    .errorMessage("孤儿任务重新分发失败: " + newSchedule.getErrorMessage())
                    .allocateTime(orphanTask.getAllocateTime())
                    .endTime(LocalDateTime.now())
                    .build();
                allocationRepository.save(failedTask);
            }

        } catch (Exception e) {
            log.error("处理孤儿任务异常: taskId={}, error={}", orphanTask.getTaskId(), e.getMessage(), e);

            // 异常情况下，将任务标记为失败
            orphanTask.setTaskStatus(TaskStatus.ERROR);
            orphanTask.setErrorMessage("处理孤儿任务异常: " + e.getMessage());
            orphanTask.setEndTime(LocalDateTime.now());
            allocationRepository.save(orphanTask);
        }
    }

    /**
     * 定时任务：重试失败任务和清理孤儿任务
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000) // 30秒
    public void scheduledTaskMaintenance() {
        try {
            log.debug("开始定时任务维护");

            // 重试失败的任务
            retryFailedTasks();

            // 清理孤儿任务
            cleanupOrphanTasks();

            log.debug("定时任务维护完成");
        } catch (Exception e) {
            log.error("定时任务维护异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 任务分配结果
     */
    @Data
    @AllArgsConstructor
    private static class TaskAllocationResult {
        private TaskAllocation allocation;
        private boolean newlyCreated;
    }
}
