package com.bohua.scheduler.repository;

import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.TaskStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务分配Repository
 */
@Repository
public interface TaskAllocationRepository extends MongoRepository<TaskAllocation, String> {
    
    /**
     * 根据服务ID查询任务分配
     */
    List<TaskAllocation> findByServiceId(String serviceId);
    
    /**
     * 根据任务状态查询
     */
    List<TaskAllocation> findByTaskStatus(TaskStatus taskStatus);

    /**
     * 根据任务状态列表查询
     */
    List<TaskAllocation> findByTaskStatusIn(List<TaskStatus> taskStatuses);

    /**
     * 根据服务ID和任务状态列表查询
     */
    List<TaskAllocation> findByServiceIdAndTaskStatusIn(String serviceId, List<TaskStatus> taskStatuses);
    
    /**
     * 根据设备ID查询
     */
    List<TaskAllocation> findByDeviceId(String deviceId);

    /**
     * 根据任务ID查询
     */
    List<TaskAllocation> findByTaskId(String taskId);

    /**
     * 根据任务ID查询活跃任务（已分配或运行中）
     */
    @Query("{'taskId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
    List<TaskAllocation> findActiveTasksByTaskId(String taskId);
    
    /**
     * 查询指定服务的活跃任务
     */
    @Query("{'serviceId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
    List<TaskAllocation> findActiveTasksByServiceId(String serviceId);
    
    /**
     * 根据时间范围查询任务分配
     */
    @Query("{'allocateTime': {$gte: ?0, $lte: ?1}}")
    List<TaskAllocation> findByAllocateTimeBetween(LocalDateTime start, LocalDateTime end);
    
    /**
     * 统计指定服务的活跃任务数量
     */
    @Query(value = "{'serviceId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}", count = true)
    long countActiveTasksByServiceId(String serviceId);
}
