package com.bohua.scheduler.service;

import com.bohua.scheduler.exception.NotFoundException;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.model.enums.TaskStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import com.bohua.scheduler.repository.TaskAllocationRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SchedulerService 测试类
 * 重点测试 Scheduler 与 Inference 服务的对接功能
 */
@RunWith(MockitoJUnitRunner.class)
public class SchedulerServiceTest {

    @Mock
    private InferenceServiceRepository serviceRepository;

    @Mock
    private TaskAllocationRepository allocationRepository;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private SchedulerService schedulerService;

    private TaskAllocation testAllocation;
    private InferenceService testService;

    @Before
    public void setUp() {
        // 创建测试用的推理服务
        testService = InferenceService.builder()
                .serviceId("test-service-001")
                .serviceName("测试推理服务")
                .baseUrl("http://localhost:9001")
                .status(ServiceStatus.ACTIVE)
                .currentQuota(1)
                .maxQuota(10)
                .build();

        // 创建测试用的任务分配
        testAllocation = TaskAllocation.builder()
                .allocationId("test-allocation-001")
                .taskId("test-task-001")
                .deviceId("test-device-001")
                .serviceId("test-service-001")
                .taskStatus(TaskStatus.RUNNING)
                .allocateTime(LocalDateTime.now().minusMinutes(10))
                .startTime(LocalDateTime.now().minusMinutes(9))
                .build();
    }

    @Test
    public void testReleaseTaskByTaskId_Success() {
        // 准备测试数据
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.of(testService));

        // 模拟推理服务删除成功
        ResponseEntity<String> successResponse = new ResponseEntity<>(
                "{\"success\": true, \"message\": \"任务删除成功\"}", 
                HttpStatus.OK);
        when(restTemplate.exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class)))
                .thenReturn(successResponse);

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果
        verify(restTemplate, times(1)).exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class));

        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() == null));
    }

    @Test
    public void testReleaseTaskByTaskId_InferenceServiceNotFound() {
        // 准备测试数据 - 推理服务在数据库中不存在
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.empty());

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果 - 不应该调用推理服务
        verify(restTemplate, never()).exchange(anyString(), any(), any(), eq(String.class));

        // 验证任务状态被更新，并记录了错误信息
        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() != null &&
                allocation.getErrorMessage().contains("推理服务记录不存在")));
    }

    @Test
    public void testReleaseTaskByTaskId_InferenceServiceUnavailable() {
        // 准备测试数据 - 推理服务不可用
        testService.setStatus(ServiceStatus.INACTIVE);
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.of(testService));

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果 - 不应该调用推理服务
        verify(restTemplate, never()).exchange(anyString(), any(), any(), eq(String.class));

        // 验证任务状态被更新，并记录了错误信息
        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() != null &&
                allocation.getErrorMessage().contains("推理服务不可用")));
    }

    @Test
    public void testReleaseTaskByTaskId_InferenceDeleteFailed() {
        // 准备测试数据
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.of(testService));

        // 模拟推理服务删除失败
        ResponseEntity<String> failureResponse = new ResponseEntity<>(
                "{\"success\": false, \"message\": \"删除失败\"}", 
                HttpStatus.INTERNAL_SERVER_ERROR);
        when(restTemplate.exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class)))
                .thenReturn(failureResponse);

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果 - 应该重试3次
        verify(restTemplate, times(3)).exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class));

        // 验证任务状态被更新，并记录了错误信息
        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() != null &&
                allocation.getErrorMessage().contains("推理服务任务删除失败")));
    }

    @Test
    public void testReleaseTaskByTaskId_InferenceTaskNotFound() {
        // 准备测试数据
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.of(testService));

        // 模拟推理服务返回404（任务不存在）
        ResponseEntity<String> notFoundResponse = new ResponseEntity<>(
                "{\"error\": \"任务不存在\"}", 
                HttpStatus.NOT_FOUND);
        when(restTemplate.exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class)))
                .thenReturn(notFoundResponse);

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果 - 只调用一次，因为404被视为成功
        verify(restTemplate, times(1)).exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class));

        // 验证任务状态被更新，没有错误信息
        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() == null));
    }

    @Test(expected = NotFoundException.class)
    public void testReleaseTaskByTaskId_TaskNotFound() {
        // 准备测试数据 - 没有活跃的任务分配
        when(allocationRepository.findActiveTasksByTaskId("non-existent-task"))
                .thenReturn(Collections.emptyList());

        // 执行测试 - 应该抛出异常
        schedulerService.releaseTaskByTaskId("non-existent-task");
    }

    @Test
    public void testReleaseTaskByTaskId_NetworkException() {
        // 准备测试数据
        when(allocationRepository.findActiveTasksByTaskId("test-task-001"))
                .thenReturn(Arrays.asList(testAllocation));
        when(serviceRepository.findById("test-service-001"))
                .thenReturn(Optional.of(testService));

        // 模拟网络异常
        when(restTemplate.exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class)))
                .thenThrow(new RuntimeException("网络连接失败"));

        // 执行测试
        schedulerService.releaseTaskByTaskId("test-task-001");

        // 验证结果 - 应该重试3次
        verify(restTemplate, times(3)).exchange(
                eq("http://localhost:9001/api/v1/tasks/test-task-001"),
                eq(HttpMethod.DELETE),
                isNull(),
                eq(String.class));

        // 验证任务状态被更新，并记录了错误信息
        verify(allocationRepository, times(1)).save(argThat(allocation -> 
                allocation.getTaskStatus() == TaskStatus.STOPPED &&
                allocation.getEndTime() != null &&
                allocation.getErrorMessage() != null &&
                allocation.getErrorMessage().contains("调用推理服务删除任务时发生异常")));
    }
}
