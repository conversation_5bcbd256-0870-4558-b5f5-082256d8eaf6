#!/bin/bash

# CV分析系统Docker部署启动脚本
# 启动scheduler和inference-mock服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start           启动所有服务 (默认)"
    echo "  start-single    只启动单个inference-mock实例"
    echo "  start-multi     启动多个inference-mock实例"
    echo "  stop            停止所有服务"
    echo "  restart         重启所有服务"
    echo "  status          查看服务状态"
    echo "  logs            查看服务日志"
    echo "  build           构建镜像"
    echo "  help            显示此帮助信息"
    echo ""
    echo "环境变量配置:"
    echo "  复制 .env.example 为 .env 并修改配置"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，使用默认配置"
        if [ -f ".env.example" ]; then
            log_info "复制.env.example为.env"
            cp .env.example .env
        fi
    fi
    
    # 检查镜像是否存在
    if ! docker images | grep -q "cv-scheduler"; then
        log_warning "cv-scheduler镜像不存在，需要先构建镜像"
        log_info "运行: ./build.sh"
        exit 1
    fi
    
    if ! docker images | grep -q "cv-inference-mock"; then
        log_warning "cv-inference-mock镜像不存在，需要先构建镜像"
        log_info "运行: ./build.sh"
        exit 1
    fi
    
    log_success "环境配置检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p inference-mock/screenshots
    mkdir -p inference-mock/screenshots-2
    mkdir -p inference-mock/logs
    mkdir -p inference-mock/logs-2
    
    log_success "目录创建完成"
}

# 启动服务
start_services() {
    local mode=${1:-"single"}
    
    log_info "启动CV分析系统服务..."
    
    create_directories
    
    if [ "$mode" = "multi" ]; then
        log_info "启动多实例模式..."
        docker-compose --profile multi-instance up -d
    else
        log_info "启动单实例模式..."
        docker-compose up -d cv-scheduler cv-inference-mock-1
    fi
    
    if [ $? -eq 0 ]; then
        log_success "服务启动成功"
        wait_for_services
        show_service_status
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待scheduler启动
    log_info "等待scheduler服务..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_success "scheduler服务已启动"
            break
        fi
        sleep 2
    done
    
    # 等待inference-mock启动
    log_info "等待inference-mock服务..."
    for i in {1..30}; do
        if curl -s http://localhost:8081/health > /dev/null 2>&1; then
            log_success "inference-mock服务已启动"
            break
        fi
        sleep 2
    done
    
    # 等待服务注册
    log_info "等待服务注册..."
    sleep 10
}

# 停止服务
stop_services() {
    log_info "停止CV分析系统服务..."
    
    docker-compose down
    
    if [ $? -eq 0 ]; then
        log_success "服务停止成功"
    else
        log_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    log_info "重启CV分析系统服务..."
    
    stop_services
    sleep 5
    start_services
}

# 显示服务状态
show_service_status() {
    echo ""
    log_info "📊 服务状态:"
    docker-compose ps
    
    echo ""
    log_info "🔗 服务地址:"
    echo "  Scheduler: http://localhost:8080"
    echo "  Scheduler健康检查: http://localhost:8080/actuator/health"
    echo "  Inference-Mock-1: http://localhost:8081"
    echo "  Inference-Mock-1健康检查: http://localhost:8081/health"
    
    if docker-compose ps | grep -q "cv-inference-mock-2"; then
        echo "  Inference-Mock-2: http://localhost:8082"
        echo "  Inference-Mock-2健康检查: http://localhost:8082/health"
    fi
    
    echo ""
    log_info "📋 API文档:"
    echo "  Scheduler API: http://localhost:8080/swagger-ui.html"
    echo ""
}

# 查看日志
show_logs() {
    log_info "查看服务日志..."
    docker-compose logs -f
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    ./build.sh
}

# 主函数
main() {
    case "${1:-start}" in
        "start"|"start-single")
            check_dependencies
            check_environment
            start_services "single"
            ;;
        "start-multi")
            check_dependencies
            check_environment
            start_services "multi"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            check_dependencies
            check_environment
            restart_services
            ;;
        "status")
            show_service_status
            ;;
        "logs")
            show_logs
            ;;
        "build")
            build_images
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
